"""
用户相关模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, Table, Text, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import List

from .base import BaseModel, StatusModel


# 用户角色关联表
user_role_table = Table(
    'user_roles',
    BaseModel.metadata,
    Column('user_id', String(36), Foreign<PERSON>ey('user.id'), primary_key=True),
    Column('role_id', String(36), Foreign<PERSON>ey('role.id'), primary_key=True),
    <PERSON>umn('created_at', DateTime(timezone=True), server_default=func.now()),
    Column('created_by', String(36))
)

# 角色权限关联表
role_permission_table = Table(
    'role_permissions',
    BaseModel.metadata,
    Column('role_id', String(36), Foreign<PERSON>ey('role.id'), primary_key=True),
    Column('permission_id', String(36), Foreign<PERSON>ey('permission.id'), primary_key=True),
    <PERSON>umn('created_at', DateTime(timezone=True), server_default=func.now()),
    Column('created_by', String(36))
)


class User(BaseModel):
    """用户模型"""
    
    __tablename__ = "user"
    
    # 基本信息
    username = Column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        comment="用户名"
    )
    
    email = Column(
        String(255),
        unique=True,
        nullable=False,
        index=True,
        comment="邮箱"
    )
    
    hashed_password = Column(
        String(255),
        nullable=False,
        comment="密码哈希"
    )
    
    full_name = Column(
        String(100),
        nullable=True,
        comment="全名"
    )
    
    phone = Column(
        String(20),
        nullable=True,
        comment="电话"
    )
    
    avatar = Column(
        String(500),
        nullable=True,
        comment="头像URL"
    )
    
    # 状态信息
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    is_superuser = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否超级用户"
    )
    
    is_verified = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已验证"
    )
    
    # 登录信息
    last_login_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后登录时间"
    )
    
    login_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="登录次数"
    )
    
    # 安全信息
    password_changed_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="密码修改时间"
    )
    
    failed_login_attempts = Column(
        Integer,
        default=0,
        nullable=False,
        comment="失败登录次数"
    )
    
    locked_until = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="锁定到期时间"
    )
    
    # 关联关系
    roles: List["Role"] = relationship(
        "Role",
        secondary=user_role_table,
        back_populates="users",
        lazy="selectin"
    )
    
    # 创建的工作流
    created_workflows = relationship(
        "Workflow",
        foreign_keys="Workflow.created_by",
        back_populates="creator",
        lazy="dynamic"
    )
    
    # 执行的任务
    task_executions = relationship(
        "TaskExecution",
        foreign_keys="TaskExecution.executed_by",
        back_populates="executor",
        lazy="dynamic"
    )
    
    def has_permission(self, permission_code: str) -> bool:
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        
        for role in self.roles:
            if role.has_permission(permission_code):
                return True
        return False
    
    def has_role(self, role_code: str) -> bool:
        """检查用户是否有指定角色"""
        return any(role.code == role_code for role in self.roles)


class Role(StatusModel):
    """角色模型"""
    
    __tablename__ = "role"
    
    code = Column(
        String(50),
        unique=True,
        nullable=False,
        index=True,
        comment="角色代码"
    )
    
    level = Column(
        Integer,
        default=0,
        nullable=False,
        comment="角色级别"
    )
    
    # 关联关系
    users: List[User] = relationship(
        "User",
        secondary=user_role_table,
        back_populates="roles",
        lazy="dynamic"
    )
    
    permissions: List["Permission"] = relationship(
        "Permission",
        secondary=role_permission_table,
        back_populates="roles",
        lazy="selectin"
    )
    
    def has_permission(self, permission_code: str) -> bool:
        """检查角色是否有指定权限"""
        return any(perm.code == permission_code for perm in self.permissions)


class Permission(StatusModel):
    """权限模型"""
    
    __tablename__ = "permission"
    
    code = Column(
        String(100),
        unique=True,
        nullable=False,
        index=True,
        comment="权限代码"
    )
    
    resource = Column(
        String(50),
        nullable=False,
        comment="资源类型"
    )
    
    action = Column(
        String(50),
        nullable=False,
        comment="操作类型"
    )
    
    # 关联关系
    roles: List[Role] = relationship(
        "Role",
        secondary=role_permission_table,
        back_populates="permissions",
        lazy="dynamic"
    )


class UserRole(BaseModel):
    """用户角色关联模型（如果需要额外字段）"""
    
    __tablename__ = "user_role"
    
    user_id = Column(
        String(36),
        ForeignKey("user.id"),
        nullable=False,
        comment="用户ID"
    )
    
    role_id = Column(
        String(36),
        ForeignKey("role.id"),
        nullable=False,
        comment="角色ID"
    )
    
    granted_by = Column(
        String(36),
        ForeignKey("user.id"),
        nullable=True,
        comment="授权者ID"
    )
    
    granted_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="授权时间"
    )
    
    expires_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="过期时间"
    )
    
    # 关联关系
    user = relationship("User", foreign_keys=[user_id])
    role = relationship("Role", foreign_keys=[role_id])
    granter = relationship("User", foreign_keys=[granted_by])
