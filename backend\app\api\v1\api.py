"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    users,
    workflows,
    tasks,
    data_sources,
    dashboard,
    system
)

api_router = APIRouter()

# 认证相关路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)

# 用户管理路由
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["用户管理"]
)

# 工作流管理路由
api_router.include_router(
    workflows.router,
    prefix="/workflows",
    tags=["工作流管理"]
)

# 任务管理路由
api_router.include_router(
    tasks.router,
    prefix="/tasks",
    tags=["任务管理"]
)

# 数据源管理路由
api_router.include_router(
    data_sources.router,
    prefix="/data-sources",
    tags=["数据源管理"]
)

# 仪表板路由
api_router.include_router(
    dashboard.router,
    prefix="/dashboard",
    tags=["仪表板"]
)

# 系统管理路由
api_router.include_router(
    system.router,
    prefix="/system",
    tags=["系统管理"]
)
