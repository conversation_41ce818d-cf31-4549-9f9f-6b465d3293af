"""
日志配置模块
"""

import sys
import logging
from pathlib import Path
from loguru import logger
from typing import Dict, Any

from app.core.config import settings


class InterceptHandler(logging.Handler):
    """拦截标准库日志并重定向到loguru"""
    
    def emit(self, record):
        # 获取对应的loguru级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        # 查找调用者
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


def setup_logging():
    """设置日志配置"""
    
    # 移除默认的loguru处理器
    logger.remove()
    
    # 控制台输出配置
    logger.add(
        sys.stderr,
        level=settings.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # 文件输出配置
    log_path = Path(settings.LOG_FILE)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    logger.add(
        log_path,
        level=settings.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=settings.LOG_ROTATION,
        retention=settings.LOG_RETENTION,
        compression="zip",
        backtrace=True,
        diagnose=True,
    )
    
    # 错误日志单独文件
    error_log_path = log_path.parent / "error.log"
    logger.add(
        error_log_path,
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=settings.LOG_ROTATION,
        retention=settings.LOG_RETENTION,
        compression="zip",
        backtrace=True,
        diagnose=True,
    )
    
    # 拦截标准库日志
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # 设置第三方库日志级别
    for logger_name in ["uvicorn", "uvicorn.error", "uvicorn.access", "fastapi"]:
        logging_logger = logging.getLogger(logger_name)
        logging_logger.handlers = [InterceptHandler()]
        logging_logger.setLevel(logging.INFO)
    
    # 数据库日志
    logging.getLogger("sqlalchemy.engine").setLevel(
        logging.INFO if settings.DEBUG else logging.WARNING
    )
    
    logger.info("日志系统初始化完成")


def get_logger(name: str = None):
    """获取logger实例"""
    if name:
        return logger.bind(name=name)
    return logger


# 日志装饰器
def log_function_call(func):
    """记录函数调用的装饰器"""
    def wrapper(*args, **kwargs):
        logger.info(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.info(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper


async def log_async_function_call(func):
    """记录异步函数调用的装饰器"""
    async def wrapper(*args, **kwargs):
        logger.info(f"调用异步函数: {func.__name__}")
        try:
            result = await func(*args, **kwargs)
            logger.info(f"异步函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper


class LoggerMixin:
    """日志混入类"""
    
    @property
    def logger(self):
        return logger.bind(name=self.__class__.__name__)


# 结构化日志记录器
class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logger.bind(name=name)
    
    def log_request(self, method: str, url: str, status_code: int, duration: float):
        """记录HTTP请求"""
        self.logger.info(
            "HTTP请求",
            extra={
                "method": method,
                "url": url,
                "status_code": status_code,
                "duration": duration,
            }
        )
    
    def log_database_query(self, query: str, duration: float, rows_affected: int = None):
        """记录数据库查询"""
        self.logger.info(
            "数据库查询",
            extra={
                "query": query,
                "duration": duration,
                "rows_affected": rows_affected,
            }
        )
    
    def log_task_execution(self, task_name: str, status: str, duration: float = None):
        """记录任务执行"""
        self.logger.info(
            "任务执行",
            extra={
                "task_name": task_name,
                "status": status,
                "duration": duration,
            }
        )
    
    def log_user_action(self, user_id: str, action: str, resource: str = None):
        """记录用户操作"""
        self.logger.info(
            "用户操作",
            extra={
                "user_id": user_id,
                "action": action,
                "resource": resource,
            }
        )
    
    def log_error(self, error: Exception, context: Dict[str, Any] = None):
        """记录错误"""
        self.logger.error(
            f"错误: {str(error)}",
            extra=context or {}
        )
