"""
企业版自动化平台 - FastAPI 主应用
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import uvicorn


# 创建FastAPI应用实例
app = FastAPI(
    title="企业版自动化平台",
    description="企业版自动化平台 - 提供工作流管理、任务调度、数据可视化等功能",
    version="1.0.0",
    openapi_url="/api/v1/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "message": "内部服务器错误",
            "detail": str(exc) if settings.DEBUG else "服务器遇到了一个错误",
            "path": str(request.url),
        },
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0",
    }


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "企业版自动化平台 API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc",
    }


# 基础API路由
@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats():
    """获取仪表板统计数据"""
    return {
        "message": "仪表板统计",
        "data": {
            "total_workflows": 5,
            "active_tasks": 12,
            "total_users": 3,
            "system_health": "healthy"
        }
    }


@app.get("/api/v1/workflows")
async def get_workflows():
    """获取工作流列表"""
    return {"message": "工作流列表", "data": []}


@app.get("/api/v1/tasks")
async def get_tasks():
    """获取任务列表"""
    return {"message": "任务列表", "data": []}


@app.get("/api/v1/users")
async def get_users():
    """获取用户列表"""
    return {"message": "用户列表", "data": []}


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
    )
