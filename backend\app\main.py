"""
企业版自动化平台 - FastAPI 主应用
"""

from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import J<PERSON>NResponse
from fastapi.staticfiles import StaticFiles
import time
import uvicorn

from app.core.config import settings
from app.core.database import engine, Base
from app.core.logging import setup_logging
from app.api.v1.api import api_router
from app.middleware.security import SecurityMiddleware
from app.middleware.rate_limit import RateLimitMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    setup_logging()
    
    # 创建数据库表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield
    
    # 关闭时执行
    await engine.dispose()


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="企业版自动化平台 - 提供工作流管理、任务调度、数据可视化等功能",
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# 添加中间件
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# 信任的主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)

# 安全中间件
app.add_middleware(SecurityMiddleware)

# 限流中间件
app.add_middleware(RateLimitMiddleware)


# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={
            "message": "内部服务器错误",
            "detail": str(exc) if settings.DEBUG else "服务器遇到了一个错误",
            "path": str(request.url),
        },
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.VERSION,
    }


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "企业版自动化平台 API",
        "version": settings.VERSION,
        "docs": "/docs",
        "redoc": "/redoc",
    }


# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
    )
