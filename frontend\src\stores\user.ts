import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    userInfo: null as any,
    permissions: [] as string[]
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
  },

  actions: {
    setToken(token: string) {
      this.token = token
    },

    setUserInfo(userInfo: any) {
      this.userInfo = userInfo
    },

    hasPermission(permission: string) {
      return this.permissions.includes(permission)
    },

    async initUser() {
      // 初始化用户信息
      console.log('用户初始化完成')
    },

    logout() {
      this.token = ''
      this.userInfo = null
      this.permissions = []
    }
  }
})
