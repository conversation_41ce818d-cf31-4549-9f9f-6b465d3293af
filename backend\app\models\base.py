"""
基础模型类
"""

from datetime import datetime
from typing import Any
from sqlalchemy import Column, Integer, DateTime, String, Boolean, Text
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.sql import func
from uuid import uuid4

from app.core.database import Base


class TimestampMixin:
    """时间戳混入类"""
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )


class SoftDeleteMixin:
    """软删除混入类"""
    
    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )
    
    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="删除时间"
    )


class UserTrackingMixin:
    """用户跟踪混入类"""
    
    created_by = Column(
        String(36),
        nullable=True,
        comment="创建者ID"
    )
    
    updated_by = Column(
        String(36),
        nullable=True,
        comment="更新者ID"
    )


class BaseModel(Base, TimestampMixin, SoftDeleteMixin, UserTrackingMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    id = Column(
        String(36),
        primary_key=True,
        default=lambda: str(uuid4()),
        comment="主键ID"
    )
    
    @declared_attr
    def __tablename__(cls) -> str:
        """自动生成表名"""
        return cls.__name__.lower()
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: dict[str, Any]) -> None:
        """从字典更新属性"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"


class NamedModel(BaseModel):
    """带名称的基础模型"""
    
    __abstract__ = True
    
    name = Column(
        String(255),
        nullable=False,
        comment="名称"
    )
    
    description = Column(
        Text,
        nullable=True,
        comment="描述"
    )
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id}, name={self.name})>"


class StatusModel(NamedModel):
    """带状态的基础模型"""
    
    __abstract__ = True
    
    status = Column(
        String(50),
        nullable=False,
        default="active",
        comment="状态"
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
