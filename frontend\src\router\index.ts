import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ 
  showSpinner: false,
  minimum: 0.2,
  easing: 'ease',
  speed: 500
})

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      layout: 'auth'
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: {
      title: '注册',
      requiresAuth: false,
      layout: 'auth'
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/Index.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true,
      icon: 'Dashboard',
      layout: 'default'
    }
  },
  {
    path: '/workflows',
    name: 'Workflows',
    component: () => import('@/views/workflow/Index.vue'),
    meta: {
      title: '工作流管理',
      requiresAuth: true,
      icon: 'Flow',
      layout: 'default'
    },
    children: [
      {
        path: '',
        name: 'WorkflowList',
        component: () => import('@/views/workflow/List.vue'),
        meta: {
          title: '工作流列表',
          requiresAuth: true
        }
      },
      {
        path: 'create',
        name: 'WorkflowCreate',
        component: () => import('@/views/workflow/Create.vue'),
        meta: {
          title: '创建工作流',
          requiresAuth: true
        }
      },
      {
        path: ':id',
        name: 'WorkflowDetail',
        component: () => import('@/views/workflow/Detail.vue'),
        meta: {
          title: '工作流详情',
          requiresAuth: true
        }
      },
      {
        path: ':id/edit',
        name: 'WorkflowEdit',
        component: () => import('@/views/workflow/Edit.vue'),
        meta: {
          title: '编辑工作流',
          requiresAuth: true
        }
      },
      {
        path: ':id/designer',
        name: 'WorkflowDesigner',
        component: () => import('@/views/workflow/Designer.vue'),
        meta: {
          title: '工作流设计器',
          requiresAuth: true,
          layout: 'fullscreen'
        }
      }
    ]
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: () => import('@/views/task/Index.vue'),
    meta: {
      title: '任务管理',
      requiresAuth: true,
      icon: 'List',
      layout: 'default'
    },
    children: [
      {
        path: '',
        name: 'TaskList',
        component: () => import('@/views/task/List.vue'),
        meta: {
          title: '任务列表',
          requiresAuth: true
        }
      },
      {
        path: 'schedules',
        name: 'TaskSchedules',
        component: () => import('@/views/task/Schedules.vue'),
        meta: {
          title: '任务调度',
          requiresAuth: true
        }
      },
      {
        path: 'executions',
        name: 'TaskExecutions',
        component: () => import('@/views/task/Executions.vue'),
        meta: {
          title: '执行记录',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/data-sources',
    name: 'DataSources',
    component: () => import('@/views/data-source/Index.vue'),
    meta: {
      title: '数据源管理',
      requiresAuth: true,
      icon: 'Database',
      layout: 'default'
    }
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/user/Index.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      permission: 'user:manage',
      icon: 'User',
      layout: 'default'
    }
  },
  {
    path: '/system',
    name: 'System',
    component: () => import('@/views/system/Index.vue'),
    meta: {
      title: '系统管理',
      requiresAuth: true,
      permission: 'system:manage',
      icon: 'Setting',
      layout: 'default'
    },
    children: [
      {
        path: 'logs',
        name: 'SystemLogs',
        component: () => import('@/views/system/Logs.vue'),
        meta: {
          title: '系统日志',
          requiresAuth: true,
          permission: 'system:logs'
        }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/Config.vue'),
        meta: {
          title: '系统配置',
          requiresAuth: true,
          permission: 'system:config'
        }
      },
      {
        path: 'monitor',
        name: 'SystemMonitor',
        component: () => import('@/views/system/Monitor.vue'),
        meta: {
          title: '系统监控',
          requiresAuth: true,
          permission: 'system:monitor'
        }
      }
    ]
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/user/Profile.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      layout: 'default'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面未找到',
      layout: 'error'
    }
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '访问被拒绝',
      layout: 'error'
    }
  },
  {
    path: '/500',
    name: 'ServerError',
    component: () => import('@/views/error/500.vue'),
    meta: {
      title: '服务器错误',
      layout: 'error'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  const userStore = useUserStore()
  const appStore = useAppStore()
  
  // 设置页面标题
  const title = to.meta.title as string
  if (title) {
    document.title = `${title} - 企业版自动化平台`
    appStore.setPageTitle(title)
  }
  
  // 检查认证要求
  if (to.meta.requiresAuth !== false) {
    if (!userStore.isAuthenticated) {
      // 未登录，重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查权限
    if (to.meta.permission) {
      const hasPermission = userStore.hasPermission(to.meta.permission as string)
      if (!hasPermission) {
        next('/403')
        return
      }
    }
  } else {
    // 已登录用户访问登录页，重定向到首页
    if (userStore.isAuthenticated && (to.path === '/login' || to.path === '/register')) {
      next('/dashboard')
      return
    }
  }
  
  next()
})

router.afterEach((to, from) => {
  // 结束进度条
  NProgress.done()
  
  // 记录路由变化
  const appStore = useAppStore()
  appStore.addVisitedView({
    path: to.path,
    name: to.name as string,
    title: to.meta.title as string,
    meta: to.meta
  })
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  NProgress.done()
})

export default router
