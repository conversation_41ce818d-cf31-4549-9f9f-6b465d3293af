"""
工作流相关模型
"""

from sqlalchemy import Column, String, Text, JSON, Integer, ForeignKey, DateTime, Boolean, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import List, Dict, Any
from enum import Enum

from .base import BaseModel, StatusModel


class WorkflowStatus(str, Enum):
    """工作流状态枚举"""
    DRAFT = "draft"          # 草稿
    ACTIVE = "active"        # 激活
    INACTIVE = "inactive"    # 停用
    ARCHIVED = "archived"    # 归档


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    SUCCESS = "success"      # 成功
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消
    TIMEOUT = "timeout"      # 超时


class NodeType(str, Enum):
    """节点类型枚举"""
    START = "start"          # 开始节点
    END = "end"              # 结束节点
    TASK = "task"            # 任务节点
    CONDITION = "condition"  # 条件节点
    PARALLEL = "parallel"    # 并行节点
    MERGE = "merge"          # 合并节点
    DELAY = "delay"          # 延迟节点
    SCRIPT = "script"        # 脚本节点
    HTTP = "http"            # HTTP请求节点
    EMAIL = "email"          # 邮件节点


class Workflow(StatusModel):
    """工作流模型"""
    
    __tablename__ = "workflow"
    
    # 基本信息
    version = Column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="版本号"
    )
    
    category = Column(
        String(50),
        nullable=True,
        comment="分类"
    )
    
    tags = Column(
        JSON,
        nullable=True,
        comment="标签"
    )
    
    # 配置信息
    config = Column(
        JSON,
        nullable=True,
        comment="工作流配置"
    )
    
    variables = Column(
        JSON,
        nullable=True,
        comment="工作流变量"
    )
    
    # 统计信息
    execution_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="执行次数"
    )
    
    success_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="成功次数"
    )
    
    failure_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="失败次数"
    )
    
    avg_duration = Column(
        Float,
        nullable=True,
        comment="平均执行时长（秒）"
    )
    
    # 关联关系
    nodes: List["WorkflowNode"] = relationship(
        "WorkflowNode",
        back_populates="workflow",
        cascade="all, delete-orphan",
        lazy="selectin"
    )
    
    executions: List["WorkflowExecution"] = relationship(
        "WorkflowExecution",
        back_populates="workflow",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )
    
    creator = relationship(
        "User",
        foreign_keys="Workflow.created_by",
        back_populates="created_workflows"
    )


class WorkflowNode(BaseModel):
    """工作流节点模型"""
    
    __tablename__ = "workflow_node"
    
    # 基本信息
    workflow_id = Column(
        String(36),
        ForeignKey("workflow.id"),
        nullable=False,
        comment="工作流ID"
    )
    
    name = Column(
        String(255),
        nullable=False,
        comment="节点名称"
    )
    
    node_type = Column(
        String(50),
        nullable=False,
        comment="节点类型"
    )
    
    # 位置信息
    position_x = Column(
        Float,
        nullable=True,
        comment="X坐标"
    )
    
    position_y = Column(
        Float,
        nullable=True,
        comment="Y坐标"
    )
    
    # 配置信息
    config = Column(
        JSON,
        nullable=True,
        comment="节点配置"
    )
    
    input_schema = Column(
        JSON,
        nullable=True,
        comment="输入数据结构"
    )
    
    output_schema = Column(
        JSON,
        nullable=True,
        comment="输出数据结构"
    )
    
    # 连接信息
    predecessors = Column(
        JSON,
        nullable=True,
        comment="前置节点ID列表"
    )
    
    successors = Column(
        JSON,
        nullable=True,
        comment="后续节点ID列表"
    )
    
    # 执行配置
    timeout = Column(
        Integer,
        nullable=True,
        comment="超时时间（秒）"
    )
    
    retry_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="重试次数"
    )
    
    retry_delay = Column(
        Integer,
        default=0,
        nullable=False,
        comment="重试延迟（秒）"
    )
    
    # 关联关系
    workflow = relationship(
        "Workflow",
        back_populates="nodes"
    )
    
    executions: List["WorkflowNodeExecution"] = relationship(
        "WorkflowNodeExecution",
        back_populates="node",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )


class WorkflowExecution(BaseModel):
    """工作流执行记录模型"""
    
    __tablename__ = "workflow_execution"
    
    # 基本信息
    workflow_id = Column(
        String(36),
        ForeignKey("workflow.id"),
        nullable=False,
        comment="工作流ID"
    )
    
    execution_id = Column(
        String(100),
        nullable=False,
        unique=True,
        index=True,
        comment="执行ID"
    )
    
    status = Column(
        String(50),
        nullable=False,
        default=ExecutionStatus.PENDING,
        comment="执行状态"
    )
    
    # 时间信息
    started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    duration = Column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    # 执行信息
    trigger_type = Column(
        String(50),
        nullable=True,
        comment="触发类型"
    )
    
    trigger_data = Column(
        JSON,
        nullable=True,
        comment="触发数据"
    )
    
    input_data = Column(
        JSON,
        nullable=True,
        comment="输入数据"
    )
    
    output_data = Column(
        JSON,
        nullable=True,
        comment="输出数据"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    error_details = Column(
        JSON,
        nullable=True,
        comment="错误详情"
    )
    
    # 关联关系
    workflow = relationship(
        "Workflow",
        back_populates="executions"
    )
    
    node_executions: List["WorkflowNodeExecution"] = relationship(
        "WorkflowNodeExecution",
        back_populates="workflow_execution",
        cascade="all, delete-orphan",
        lazy="selectin"
    )


class WorkflowNodeExecution(BaseModel):
    """工作流节点执行记录模型"""
    
    __tablename__ = "workflow_node_execution"
    
    # 基本信息
    workflow_execution_id = Column(
        String(36),
        ForeignKey("workflow_execution.id"),
        nullable=False,
        comment="工作流执行ID"
    )
    
    node_id = Column(
        String(36),
        ForeignKey("workflow_node.id"),
        nullable=False,
        comment="节点ID"
    )
    
    status = Column(
        String(50),
        nullable=False,
        default=ExecutionStatus.PENDING,
        comment="执行状态"
    )
    
    # 时间信息
    started_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="开始时间"
    )
    
    completed_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    duration = Column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    # 执行信息
    input_data = Column(
        JSON,
        nullable=True,
        comment="输入数据"
    )
    
    output_data = Column(
        JSON,
        nullable=True,
        comment="输出数据"
    )
    
    error_message = Column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    
    retry_count = Column(
        Integer,
        default=0,
        nullable=False,
        comment="重试次数"
    )
    
    # 关联关系
    workflow_execution = relationship(
        "WorkflowExecution",
        back_populates="node_executions"
    )
    
    node = relationship(
        "WorkflowNode",
        back_populates="executions"
    )
