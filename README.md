# 企业版自动化平台

一个基于 FastAPI + Vue3 的现代化企业级自动化平台，提供工作流管理、任务调度、数据可视化等核心功能。

## 🚀 功能特性

### 核心功能
- **用户认证与权限管理** - JWT认证、角色权限控制
- **工作流设计器** - 可视化流程设计、拖拽式节点编辑
- **任务调度系统** - 定时任务、触发器管理、执行监控
- **数据管理** - 数据源连接、数据处理、ETL流程
- **可视化仪表板** - 实时监控、图表展示、报表生成
- **系统管理** - 日志管理、系统配置、性能监控

### 技术特性
- **高性能** - 异步处理、缓存优化、数据库连接池
- **可扩展** - 微服务架构、插件系统、API优先设计
- **安全可靠** - 数据加密、访问控制、审计日志
- **易部署** - Docker容器化、一键部署、环境隔离

## 🛠 技术栈

### 后端技术
- **FastAPI** - 现代化Python Web框架
- **SQLAlchemy** - 强大的ORM框架
- **PostgreSQL** - 企业级关系数据库
- **Redis** - 高性能缓存和消息队列
- **Celery** - 分布式任务队列
- **Pydantic** - 数据验证和序列化

### 前端技术
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速构建工具
- **Element Plus** - 企业级UI组件库
- **Pinia** - 现代状态管理
- **ECharts** - 数据可视化图表库

### 部署技术
- **Docker** - 容器化部署
- **Nginx** - 反向代理和负载均衡
- **GitHub Actions** - CI/CD自动化

## 📁 项目结构

```
enterprise-automation-platform/
├── backend/                 # 后端服务
│   ├── app/                # 应用核心代码
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   ├── utils/         # 工具函数
│   │   └── main.py        # 应用入口
│   ├── alembic/           # 数据库迁移
│   ├── tests/             # 测试代码
│   ├── requirements.txt   # Python依赖
│   └── Dockerfile         # Docker配置
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── components/    # 组件
│   │   ├── views/         # 页面
│   │   ├── stores/        # 状态管理
│   │   ├── router/        # 路由配置
│   │   ├── api/           # API调用
│   │   └── main.ts        # 应用入口
│   ├── public/            # 静态资源
│   ├── package.json       # 依赖配置
│   └── Dockerfile         # Docker配置
├── docker-compose.yml      # 容器编排
├── nginx.conf             # Nginx配置
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求
- Python 3.9+
- Node.js 16+
- PostgreSQL 12+
- Redis 6+
- Docker & Docker Compose

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd enterprise-automation-platform
```

2. **启动后端服务**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

3. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

4. **使用Docker Compose**
```bash
docker-compose up -d
```

### 访问地址
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- 管理后台: http://localhost:3000/admin

## 📖 API文档

项目提供完整的API文档，包括：
- 自动生成的OpenAPI规范
- 交互式API测试界面
- 详细的请求/响应示例
- 认证和权限说明

访问 http://localhost:8000/docs 查看完整API文档。

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/dbname
REDIS_URL=redis://localhost:6379

# 安全配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 应用配置
DEBUG=false
CORS_ORIGINS=["http://localhost:3000"]
```

### 数据库迁移
```bash
cd backend
alembic upgrade head
```

## 🧪 测试

### 后端测试
```bash
cd backend
pytest
```

### 前端测试
```bash
cd frontend
npm run test
```

## 📦 部署

### 生产环境部署
```bash
# 构建镜像
docker-compose -f docker-compose.prod.yml build

# 启动服务
docker-compose -f docker-compose.prod.yml up -d
```

### 性能优化
- 启用Redis缓存
- 配置数据库连接池
- 使用CDN加速静态资源
- 启用Gzip压缩

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: <EMAIL>

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！
