<template>
  <div class="dashboard">
    <el-container>
      <el-header>
        <h1>企业版自动化平台</h1>
      </el-header>
      <el-main>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card>
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalWorkflows }}</div>
                <div class="stat-label">工作流总数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <div class="stat-card">
                <div class="stat-number">{{ stats.activeTasks }}</div>
                <div class="stat-label">活跃任务</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <div class="stat-card">
                <div class="stat-number">{{ stats.totalUsers }}</div>
                <div class="stat-label">用户总数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card>
              <div class="stat-card">
                <div class="stat-number">{{ stats.systemHealth }}</div>
                <div class="stat-label">系统状态</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card>
              <template #header>
                <span>欢迎使用企业版自动化平台</span>
              </template>
              <p>这是一个基于 FastAPI + Vue3 构建的现代化企业级自动化平台。</p>
              <p>主要功能包括：</p>
              <ul>
                <li>工作流设计和管理</li>
                <li>任务调度和监控</li>
                <li>数据源管理</li>
                <li>用户权限管理</li>
                <li>系统监控和日志</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const stats = ref({
  totalWorkflows: 0,
  activeTasks: 0,
  totalUsers: 0,
  systemHealth: 'healthy'
})

onMounted(async () => {
  try {
    // 这里可以调用API获取实际数据
    console.log('仪表板加载完成')
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
})
</script>

<style scoped>
.dashboard {
  height: 100vh;
  background-color: #f5f5f5;
}

.el-header {
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
}

.el-main {
  padding: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  margin-top: 10px;
  color: #666;
}

ul {
  margin-left: 20px;
}

li {
  margin: 5px 0;
}
</style>
