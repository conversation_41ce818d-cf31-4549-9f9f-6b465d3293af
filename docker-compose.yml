version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: automation_postgres
    environment:
      POSTGRES_DB: automation_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - automation_network
    restart: unless-stopped

  # Redis 缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: automation_redis
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - automation_network
    restart: unless-stopped

  # FastAPI 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: automation_backend
    environment:
      - DATABASE_URL=***********************************************/automation_platform
      - REDIS_URL=redis://:redis123@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - CORS_ORIGINS=["http://localhost:3000", "http://frontend:3000"]
      - DEBUG=true
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    networks:
      - automation_network
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker 异步任务处理
  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: automation_celery_worker
    environment:
      - DATABASE_URL=***********************************************/automation_platform
      - REDIS_URL=redis://:redis123@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production
    volumes:
      - ./backend:/app
      - celery_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - automation_network
    restart: unless-stopped
    command: celery -A app.core.celery worker --loglevel=info

  # Celery Beat 定时任务调度
  celery_beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: automation_celery_beat
    environment:
      - DATABASE_URL=***********************************************/automation_platform
      - REDIS_URL=redis://:redis123@redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-in-production
    volumes:
      - ./backend:/app
      - celery_logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - automation_network
    restart: unless-stopped
    command: celery -A app.core.celery beat --loglevel=info

  # Vue3 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: automation_frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_WS_BASE_URL=ws://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - automation_network
    restart: unless-stopped
    command: npm run dev -- --host 0.0.0.0

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: automation_nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./frontend/dist:/usr/share/nginx/html
      - nginx_logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - frontend
    networks:
      - automation_network
    restart: unless-stopped

  # Flower Celery 监控
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: automation_flower
    environment:
      - REDIS_URL=redis://:redis123@redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - automation_network
    restart: unless-stopped
    command: celery -A app.core.celery flower --port=5555

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  celery_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  automation_network:
    driver: bridge
