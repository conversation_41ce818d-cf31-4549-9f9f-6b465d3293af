"""
简化的后端服务器
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import time

# 创建FastAPI应用
app = FastAPI(
    title="企业版自动化平台",
    description="企业版自动化平台 - 提供工作流管理、任务调度、数据可视化等功能",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": "1.0.0",
    }

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "企业版自动化平台 API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc",
    }

# 仪表板API
@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats():
    """获取仪表板统计数据"""
    return {
        "message": "仪表板统计",
        "data": {
            "total_workflows": 5,
            "active_tasks": 12,
            "total_users": 3,
            "system_health": "healthy"
        }
    }

# 工作流API
@app.get("/api/v1/workflows")
async def get_workflows():
    """获取工作流列表"""
    return {
        "message": "工作流列表", 
        "data": [
            {"id": "1", "name": "数据处理工作流", "status": "active"},
            {"id": "2", "name": "报表生成工作流", "status": "inactive"},
        ]
    }

# 任务API
@app.get("/api/v1/tasks")
async def get_tasks():
    """获取任务列表"""
    return {
        "message": "任务列表", 
        "data": [
            {"id": "1", "name": "数据同步任务", "status": "running"},
            {"id": "2", "name": "备份任务", "status": "pending"},
        ]
    }

# 用户API
@app.get("/api/v1/users")
async def get_users():
    """获取用户列表"""
    return {
        "message": "用户列表", 
        "data": [
            {"id": "1", "username": "admin", "email": "<EMAIL>"},
            {"id": "2", "username": "user1", "email": "<EMAIL>"},
        ]
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动企业版自动化平台后端服务...")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔧 健康检查: http://localhost:8000/health")
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
