#!/usr/bin/env python3
"""
后端启动脚本
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装依赖"""
    print("正在安装后端依赖...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "fastapi==0.104.1",
            "uvicorn[standard]==0.24.0",
            "python-multipart==0.0.6",
            "pydantic==2.5.0",
            "pydantic-settings==2.1.0"
        ], check=True)
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    return True

def start_server():
    """启动服务器"""
    print("正在启动FastAPI服务器...")
    os.chdir("backend")
    try:
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务器启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    return True

if __name__ == "__main__":
    print("🚀 企业版自动化平台 - 后端启动")
    
    if install_dependencies():
        start_server()
    else:
        print("❌ 启动失败")
        sys.exit(1)
