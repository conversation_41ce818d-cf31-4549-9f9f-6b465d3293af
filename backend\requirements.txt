# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库相关
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# 数据验证和序列化
pydantic==2.5.0
pydantic-settings==2.1.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 缓存和任务队列
redis==5.0.1
celery==5.3.4
flower==2.0.1

# HTTP 客户端
httpx==0.25.2
aiofiles==23.2.1

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 工具库
python-dateutil==2.8.2
pytz==2023.3
email-validator==2.1.0
jinja2==3.1.2

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8

# 其他工具
click==8.1.7
rich==13.7.0
typer==0.9.0
