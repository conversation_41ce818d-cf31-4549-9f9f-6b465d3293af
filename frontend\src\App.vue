<template>
  <div id="app" class="app-container">
    <!-- 全局加载条 -->
    <div v-if="loading" class="global-loading">
      <el-loading-service />
    </div>
    
    <!-- 路由视图 -->
    <router-view v-slot="{ Component, route }">
      <transition
        :name="route.meta.transition || 'fade'"
        mode="out-in"
        appear
      >
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
    
    <!-- 全局通知 -->
    <GlobalNotification />
    
    <!-- 全局确认对话框 -->
    <GlobalConfirm />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import GlobalNotification from '@/components/global/GlobalNotification.vue'
import GlobalConfirm from '@/components/global/GlobalConfirm.vue'

// 应用状态
const appStore = useAppStore()
const userStore = useUserStore()

// 计算属性
const loading = computed(() => appStore.loading)
const cachedViews = computed(() => appStore.cachedViews)

// 初始化应用
onMounted(async () => {
  try {
    // 初始化用户信息
    await userStore.initUser()
    
    // 初始化应用配置
    await appStore.initApp()
    
    console.log('✅ 应用初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
  }
})

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    // 更新面包屑
    appStore.updateBreadcrumb(newPath)
    
    // 记录页面访问
    appStore.recordPageVisit(newPath)
  },
  { immediate: true }
)

// 监听主题变化
watch(
  () => appStore.theme,
  (theme) => {
    document.documentElement.className = theme
  },
  { immediate: true }
)

// 监听语言变化
watch(
  () => appStore.locale,
  (locale) => {
    document.documentElement.lang = locale
  },
  { immediate: true }
)
</script>

<style lang="scss">
.app-container {
  height: 100vh;
  overflow: hidden;
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-left-enter-active,
.slide-left-leave-active {
  transition: all 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease;
}

.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    font-size: 14px;
  }
}

// 暗色主题
.dark {
  .global-loading {
    background: rgba(0, 0, 0, 0.9);
  }
}
</style>
