import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    loading: false,
    theme: 'light',
    locale: 'zh-CN',
    cachedViews: [] as string[],
    visitedViews: [] as any[],
    pageTitle: '企业版自动化平台'
  }),

  actions: {
    setLoading(loading: boolean) {
      this.loading = loading
    },

    setTheme(theme: string) {
      this.theme = theme
    },

    setPageTitle(title: string) {
      this.pageTitle = title
    },

    addVisitedView(view: any) {
      if (!this.visitedViews.find(v => v.path === view.path)) {
        this.visitedViews.push(view)
      }
    },

    updateBreadcrumb(path: string) {
      // 更新面包屑逻辑
    },

    recordPageVisit(path: string) {
      // 记录页面访问逻辑
    },

    async initApp() {
      // 初始化应用配置
      console.log('应用初始化完成')
    }
  }
})
