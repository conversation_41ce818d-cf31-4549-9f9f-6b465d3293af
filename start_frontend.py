#!/usr/bin/env python3
"""
前端启动脚本
"""

import subprocess
import sys
import os

def check_node():
    """检查Node.js是否安装"""
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Node.js未安装，请先安装Node.js 16+")
    return False

def install_dependencies():
    """安装依赖"""
    print("正在安装前端依赖...")
    try:
        subprocess.run(["npm", "install"], check=True, cwd="frontend")
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ npm未找到，请确保Node.js已正确安装")
        return False
    return True

def start_dev_server():
    """启动开发服务器"""
    print("正在启动Vue3开发服务器...")
    try:
        subprocess.run(["npm", "run", "dev"], check=True, cwd="frontend")
    except subprocess.CalledProcessError as e:
        print(f"❌ 开发服务器启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n🛑 开发服务器已停止")
    except FileNotFoundError:
        print("❌ npm未找到，请确保Node.js已正确安装")
        return False
    return True

if __name__ == "__main__":
    print("🎨 企业版自动化平台 - 前端启动")
    
    if check_node() and install_dependencies():
        start_dev_server()
    else:
        print("❌ 启动失败")
        sys.exit(1)
